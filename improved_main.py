#!/usr/bin/env python3
"""
改进版本的DropShelf主应用程序
实现所有用户要求的功能：
1. 相同文件不能重复拖进去
2. 统一展示为一个图标，显示文件数量
3. 点击图标展开/收起文件列表
4. 只在拖动文件时检测抖动，窗口显示时停止检测
"""

import sys
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer


class ImprovedDropShelfApp:
    """改进版本的DropShelf应用程序"""

    def __init__(self):
        self.app = None
        self.shelf_widget = None
        self.gesture_detector = None
        self.shelf_visible = False
        self.init_timer = None
        self.close_timer = None

    def setup_application(self):
        """设置应用程序"""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("DropShelf Improved")
        self.app.setApplicationVersion("2.0.0")
        self.app.setOrganizationName("DropShelf")
        print("✅ 应用程序框架设置完成")

    def create_shelf_widget(self):
        """创建shelf窗口（延迟导入）"""
        try:
            print("📦 正在初始化shelf组件...")
            # 延迟导入以加快启动速度
            from improved_shelf_widget import ImprovedShelfWidget

            self.shelf_widget = ImprovedShelfWidget()

            # 连接信号
            self.shelf_widget.shelf_shown.connect(self.on_shelf_shown)
            self.shelf_widget.shelf_hidden.connect(self.on_shelf_hidden)

            # 初始隐藏
            self.shelf_widget.hide()

            print("✅ Shelf窗口创建完成")
            return True

        except Exception as e:
            print(f"❌ 创建shelf窗口时出错: {e}")
            return False

    def setup_gesture_detector(self):
        """设置手势检测（延迟导入）"""
        try:
            print("🖱️ 正在初始化手势检测...")
            # 延迟导入以加快启动速度
            from gesture_detector import GestureDetector

            self.gesture_detector = GestureDetector()

            # 连接信号
            self.gesture_detector.shake_detected.connect(self.on_shake_detected)
            self.gesture_detector.drag_started.connect(self.on_drag_started)
            self.gesture_detector.drag_ended.connect(self.on_drag_ended)

            # 启动检测
            self.gesture_detector.start_detection()

            print("✅ 手势检测设置完成")
            return True

        except Exception as e:
            print(f"❌ 设置手势检测时出错: {e}")
            return False

    def delayed_init(self):
        """延迟初始化组件"""
        print("⚡ 正在后台初始化组件...")

        # 创建shelf窗口
        if not self.create_shelf_widget():
            return False

        # 显示启动成功窗口
        self.show_startup_success()

        # 设置手势检测
        gesture_ok = self.setup_gesture_detector()

        # 显示完成信息
        self.show_ready_message(gesture_ok)
        return True

    def show_startup_success(self):
        """显示启动成功窗口，预热组件"""
        print("🎉 显示启动成功窗口...")

        # 获取屏幕中心位置
        screen = self.app.primaryScreen().geometry()
        center_x = screen.width() // 2 - 100
        center_y = screen.height() // 2 - 50

        # 临时显示窗口以预热组件
        self.shelf_widget.show_startup_message(center_x, center_y)

        # 1秒后自动关闭
        self.close_timer = QTimer()
        self.close_timer.timeout.connect(self.hide_startup_window)
        self.close_timer.setSingleShot(True)
        self.close_timer.start(1000)  # 1秒后关闭

    def hide_startup_window(self):
        """隐藏启动窗口"""
        print("✅ 启动窗口已关闭，组件预热完成")
        # 清除启动消息并隐藏窗口
        self.shelf_widget.clear_startup_message()
        self.shelf_widget.hide()
        # 确保状态正确
        self.shelf_visible = False

    def show_ready_message(self, gesture_ok):
        """显示就绪消息"""
        print("\n" + "=" * 60)
        print("🎉 DropShelf 改进版已就绪！")
        print("=" * 60)
        print("✨ 功能:")
        print("  📦 统一图标显示所有文件")
        print("  🔄 单击图标展开/收起文件列表")
        print("  🚫 自动去重，相同文件不会重复添加")
        print("  🖱️ 只在拖拽文件时检测抖动手势")
        print("  🎯 窗口显示时停止手势检测，避免位置变化")
        print("  🖱️ 右键点击窗口任意位置关闭shelf")
        print()
        print("📖 使用方法:")
        if gesture_ok:
            print("  1. 🖱️ 拖拽文件时快速左右抖动鼠标召唤shelf")
            print("  2. 📁 拖拽文件到shelf进行存储")
            print("  3. 🔄 单击图标展开查看所有文件")
            print("  4. 🚀 拖拽图标可一次性拖出所有文件")
            print("  5. 📄 拖拽展开列表中的单个文件")
            print("  6. 🚪 按Esc键或右键点击窗口关闭shelf")
        else:
            print("  ⚠️ 手势检测失败，请手动测试")
        print("=" * 60)
    
    def on_shake_detected(self, x, y):
        """处理抖动手势检测"""
        # 只有在窗口隐藏时才响应抖动
        if not self.shelf_visible:
            print(f"🎉 检测到抖动手势 at ({x}, {y}) - 显示shelf")

            # 计算shelf位置 - 窗口左下角在鼠标位置
            shelf_x = x  # 窗口左边缘在鼠标x位置
            shelf_y = y - self.shelf_widget.height()  # 窗口下边缘在鼠标y位置

            # 显示shelf
            self.shelf_widget.show_at_position(shelf_x, shelf_y)
        else:
            print("⚠️ Shelf已显示，忽略抖动手势")
    
    def on_drag_started(self):
        """处理拖拽开始事件"""
        print("📁 开始拖拽文件")
    
    def on_drag_ended(self):
        """处理拖拽结束事件"""
        print("📁 拖拽文件结束")
    
    def on_shelf_shown(self):
        """处理shelf显示事件"""
        self.shelf_visible = True
        print("👁️ Shelf已显示 - 停止手势检测")
    
    def on_shelf_hidden(self):
        """处理shelf隐藏事件"""
        self.shelf_visible = False
        print("🙈 Shelf已隐藏 - 恢复手势检测")
    
    def run(self):
        """运行应用程序"""
        try:
            print("🚀 启动改进版DropShelf...")

            # 1. 快速设置应用程序框架
            self.setup_application()

            # 2. 使用定时器异步初始化组件
            self.init_timer = QTimer()
            self.init_timer.timeout.connect(self.delayed_init)
            self.init_timer.setSingleShot(True)
            self.init_timer.start(100)  # 100ms后开始初始化

            print("⚡ 应用程序启动完成，正在后台初始化组件...")

            # 启动事件循环
            return self.app.exec()

        except KeyboardInterrupt:
            print("\n⚠️ 用户中断应用程序")
            return 0
        except Exception as e:
            print(f"\n❌ 致命错误: {e}")
            import traceback
            traceback.print_exc()
            return 1
        finally:
            # 清理资源
            if self.gesture_detector:
                try:
                    self.gesture_detector.stop_detection()
                    print("🧹 手势检测已停止")
                except Exception:  # pylint: disable=broad-except
                    pass


def check_dependencies():
    """快速检查关键依赖项"""
    try:
        # 只检查最关键的依赖项以加快启动速度
        import PyQt6.QtWidgets  # pylint: disable=import-outside-toplevel,unused-import
        import PyQt6.QtCore  # pylint: disable=import-outside-toplevel,unused-import
        return True
    except ImportError as e:
        print(f"❌ 缺少关键依赖项: {e}")
        print("请安装依赖项: pip install PyQt6 pynput pywin32")
        return False


def main():
    """主函数"""
    print("=" * 70)
    print("DropShelf - 改进版 Windows 生产力工具")
    print("=" * 70)
    print("⚡ 快速启动模式 - 组件将在后台异步加载")

    # 快速检查依赖项
    if not check_dependencies():
        return 1

    # 创建并运行应用程序
    app = ImprovedDropShelfApp()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
